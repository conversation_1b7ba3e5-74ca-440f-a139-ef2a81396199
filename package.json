{"name": "@srigi/mcp-google-images-search", "version": "0.0.1", "scripts": {"build": "tsc && tsc-alias", "dev": "chokidar 'src/**/*.ts' -c 'tsc && tsc-alias'", "lint": "eslint .", "lint:fix": "eslint . --fix", "tsc": "tsc -noEmit", "test": "vitest run"}, "dependencies": {"@modelcontextprotocol/sdk": "~1.13", "winston": "~3.17", "zod": "~3.25"}, "devDependencies": {"@eslint/js": "~9.29", "@types/node": "^24", "chokidar-cli": "~3.0", "eslint": "~9.29", "eslint-config-prettier": "~10.1", "eslint-plugin-prettier": "~5.5", "prettier": "~3.6", "tsc-alias": "~1.8", "typescript": "~5.8", "typescript-eslint": "~8.35", "vitest": "~3.2"}, "packageManager": "pnpm@10.12.3+sha512.467df2c586056165580ad6dfb54ceaad94c5a30f80893ebdec5a44c5aa73c205ae4a5bb9d5ed6bb84ea7c249ece786642bbb49d06a307df218d03da41c317417"}