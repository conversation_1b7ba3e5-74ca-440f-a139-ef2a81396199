# persist_image Tool

Downloads images from URLs and saves them to the local project directory with proper validation and security checks.

## Features

- **URL Validation**: Validates that the provided URL is properly formatted
- **Path Security**: Ensures target paths are within the project directory to prevent directory traversal attacks
- **Content Type Validation**: Only allows downloading of image files (JPEG, PNG, GIF, WebP, SVG, BMP, TIFF, AVIF)
- **File Size Limits**: Prevents downloading files larger than 10MB
- **Automatic Extension Detection**: Infers file extensions from MIME types when not provided
- **Directory Creation**: Automatically creates parent directories if they don't exist
- **Filename Inference**: Extracts filenames from URLs when only a directory path is provided

## Parameters

- `link` (string, required): URL to the full-quality image to download
- `path` (string, required): Relative path where to save the image (can be folder or folder/filename.ext)

## Path Handling

The tool supports flexible path specifications:

1. **Full path with extension**: `images/photo.jpg`
   - Saves the file exactly as specified
   
2. **Path without extension**: `images/photo`
   - Automatically adds extension based on the image's MIME type
   
3. **Directory only**: `images/`
   - Uses the filename from the URL and adds appropriate extension

## Security Features

- **Directory Traversal Protection**: Prevents saving files outside the project directory
- **Content Type Validation**: Only downloads files with valid image MIME types
- **File Size Limits**: Rejects files larger than 10MB (both by Content-Length header and actual file size)
- **Path Validation**: Ensures all paths are safe and within project bounds

## Error Handling

The tool provides detailed error messages for various failure scenarios:

- `INVALID_PATH`: Target path is outside project directory
- `FETCH_FAILED`: Network error during download
- `HTTP_ERROR`: HTTP error response (404, 403, etc.)
- `INVALID_CONTENT_TYPE`: File is not a valid image type
- `FILE_TOO_LARGE`: File exceeds size limits
- `DIRECTORY_CREATE_FAILED`: Cannot create parent directories
- `SAVE_FAILED`: Error writing file to disk

## Usage Examples

```typescript
// Download to specific file
await persist_image({
  link: "https://example.com/image.jpg",
  path: "assets/downloaded-image.jpg"
});

// Download with auto-extension
await persist_image({
  link: "https://example.com/image.png",
  path: "assets/downloaded-image"  // Will become downloaded-image.png
});

// Download to directory with filename from URL
await persist_image({
  link: "https://example.com/photos/sunset.jpg",
  path: "assets/"  // Will become assets/sunset.jpg
});
```

## Supported Image Types

- JPEG (image/jpeg, image/jpg)
- PNG (image/png)
- GIF (image/gif)
- WebP (image/webp)
- SVG (image/svg+xml)
- BMP (image/bmp)
- TIFF (image/tiff)
- AVIF (image/avif)
