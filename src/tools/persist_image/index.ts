import type { ToolCallback } from '@modelcontextprotocol/sdk/server/mcp.js';
import type { Logger } from 'winston';
import { z } from 'zod';

import { PersistImageError, downloadAndSaveImage } from './utils.js';

export const schema = {
  link: z.string().url().describe('URL to the full-quality image'),
  path: z.string().describe('Relative path where to save the image (can be folder or folder/filename.ext)'),
} as const;

export function getHandler(logger: Logger) {
  const handler: ToolCallback<typeof schema> = async ({ link, path }) => {
    logger.info('persist_image() tool called', { link, path });
    const _meta: Record<string, unknown> = {};

    try {
      const result = await downloadAndSaveImage({ url: link, targetPath: path });
      _meta.success = true;
      _meta.savedPath = result.savedPath;
      _meta.fileSize = result.fileSize;
      _meta.mimeType = result.mimeType;

      logger.info('persist_image() success', { result, _meta });

      return {
        _meta,
        content: [
          {
            type: 'text' as const,
            text: `Image successfully downloaded and saved to: ${result.savedPath}\nFile size: ${Math.round(result.fileSize / 1024)}KB\nMIME type: ${result.mimeType}`,
          },
        ],
      };
    } catch (err: unknown) {
      _meta.error =
        err instanceof PersistImageError
          ? {
              type: 'PersistImageError',
              message: err.message,
              code: err.code,
            }
          : {
              type: 'UnknownError',
              message: err instanceof Error ? err.message : 'Unknown error occurred',
            };

      logger.error('persist_image() error', { error: _meta.error });

      return {
        _meta,
        content: [
          {
            type: 'text' as const,
            text: `Error: ${err instanceof Error ? err.message : 'Unknown error occurred'}`,
          },
        ],
      };
    }
  };

  return handler;
}
