import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { promises as fs } from 'node:fs';
import { join, resolve } from 'node:path';
import { tmpdir } from 'node:os';

import { downloadAndSaveImage, PersistImageError } from './utils';

// Mock fetch for testing
const originalFetch = global.fetch;

describe('persist_image/utils', () => {
  let testDir: string;
  let originalCwd: string;

  beforeEach(async () => {
    // Create a temporary directory for testing
    testDir = await fs.mkdtemp(join(tmpdir(), 'persist-image-test-'));
    originalCwd = process.cwd();
    process.chdir(testDir);
  });

  afterEach(async () => {
    // Restore original fetch and cwd
    global.fetch = originalFetch;
    process.chdir(originalCwd);

    // Clean up test directory
    try {
      await fs.rm(testDir, { recursive: true, force: true });
    } catch {
      // Ignore cleanup errors
    }
  });

  describe('downloadAndSaveImage', () => {
    it('should successfully download and save a valid image', async () => {
      // Mock a successful image response
      const mockImageData = Buffer.from('fake-image-data');
      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Map([
          ['content-type', 'image/jpeg'],
          ['content-length', mockImageData.length.toString()],
        ]),
        body: {
          [Symbol.asyncIterator]: async function* () {
            yield mockImageData;
          },
        },
      });

      const result = await downloadAndSaveImage({
        url: 'https://example.com/image.jpg',
        targetPath: 'test-image.jpg',
      });

      expect(result.savedPath).toBe('test-image.jpg');
      expect(result.mimeType).toBe('image/jpeg');
      expect(result.fileSize).toBe(mockImageData.length);

      // Verify file was actually saved
      const savedFile = await fs.readFile('test-image.jpg');
      expect(savedFile).toEqual(mockImageData);
    });

    it('should infer extension from MIME type when no extension provided', async () => {
      const mockImageData = Buffer.from('fake-png-data');
      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Map([
          ['content-type', 'image/png'],
          ['content-length', mockImageData.length.toString()],
        ]),
        body: {
          [Symbol.asyncIterator]: async function* () {
            yield mockImageData;
          },
        },
      });

      const result = await downloadAndSaveImage({
        url: 'https://example.com/image',
        targetPath: 'test-image',
      });

      expect(result.savedPath).toBe('test-image.png');
      expect(result.mimeType).toBe('image/png');
    });

    it('should create directories if they do not exist', async () => {
      const mockImageData = Buffer.from('fake-image-data');
      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Map([
          ['content-type', 'image/jpeg'],
          ['content-length', mockImageData.length.toString()],
        ]),
        body: {
          [Symbol.asyncIterator]: async function* () {
            yield mockImageData;
          },
        },
      });

      const result = await downloadAndSaveImage({
        url: 'https://example.com/image.jpg',
        targetPath: 'nested/folder/image.jpg',
      });

      expect(result.savedPath).toBe('nested/folder/image.jpg');

      // Verify directory was created
      const stats = await fs.stat('nested/folder');
      expect(stats.isDirectory()).toBe(true);
    });

    it('should reject non-image content types', async () => {
      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Map([['content-type', 'text/html']]),
        body: null,
      });

      await expect(
        downloadAndSaveImage({
          url: 'https://example.com/not-an-image.html',
          targetPath: 'test.jpg',
        }),
      ).rejects.toThrow(PersistImageError);
    });

    it('should reject files that are too large', async () => {
      const largeSize = (11 * 1024 * 1024).toString(); // 11MB
      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Map([
          ['content-type', 'image/jpeg'],
          ['content-length', largeSize],
        ]),
        body: null,
      });

      await expect(
        downloadAndSaveImage({
          url: 'https://example.com/large-image.jpg',
          targetPath: 'test.jpg',
        }),
      ).rejects.toThrow(PersistImageError);
    });

    it('should reject paths outside project directory', async () => {
      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Map([['content-type', 'image/jpeg']]),
        body: null,
      });

      await expect(
        downloadAndSaveImage({
          url: 'https://example.com/image.jpg',
          targetPath: '../outside-project.jpg',
        }),
      ).rejects.toThrow(PersistImageError);
    });

    it('should handle network errors', async () => {
      global.fetch = vi.fn().mockRejectedValue(new Error('Network error'));

      await expect(
        downloadAndSaveImage({
          url: 'https://example.com/image.jpg',
          targetPath: 'test.jpg',
        }),
      ).rejects.toThrow(PersistImageError);
    });

    it('should handle HTTP errors', async () => {
      global.fetch = vi.fn().mockResolvedValue({
        ok: false,
        status: 404,
        statusText: 'Not Found',
      });

      await expect(
        downloadAndSaveImage({
          url: 'https://example.com/nonexistent.jpg',
          targetPath: 'test.jpg',
        }),
      ).rejects.toThrow(PersistImageError);
    });
  });
});
