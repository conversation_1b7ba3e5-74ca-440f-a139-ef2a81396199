import { createWriteStream, promises as fs } from 'node:fs';
import { dirname, extname, join, resolve, relative } from 'node:path';
import { pipeline } from 'node:stream/promises';

export interface DownloadOptions {
  url: string;
  targetPath: string;
}

export interface DownloadResult {
  savedPath: string;
  fileSize: number;
  mimeType: string;
}

export class PersistImageError extends Error {
  constructor(
    message: string,
    public code: string,
  ) {
    super(message);
    this.name = 'PersistImageError';
  }
}

// Allowed image MIME types
const ALLOWED_IMAGE_TYPES = new Set([
  'image/jpeg',
  'image/jpg',
  'image/png',
  'image/gif',
  'image/webp',
  'image/svg+xml',
  'image/bmp',
  'image/tiff',
  'image/avif',
]);

// Maximum file size (10MB)
const MAX_FILE_SIZE = 10 * 1024 * 1024;

/**
 * Gets file extension from MIME type
 */
function getExtensionFromMimeType(mimeType: string): string {
  const mimeToExt: Record<string, string> = {
    'image/jpeg': '.jpg',
    'image/jpg': '.jpg',
    'image/png': '.png',
    'image/gif': '.gif',
    'image/webp': '.webp',
    'image/svg+xml': '.svg',
    'image/bmp': '.bmp',
    'image/tiff': '.tiff',
    'image/avif': '.avif',
  };
  return mimeToExt[mimeType] || '.jpg';
}

/**
 * Extracts filename from URL
 */
function getFilenameFromUrl(url: string): string {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    const filename = pathname.split('/').pop() || 'image';
    return filename.includes('.') ? filename : 'image';
  } catch {
    return 'image';
  }
}

/**
 * Validates that the target path is safe and within project bounds
 */
async function validateTargetPath(targetPath: string): Promise<string> {
  // Resolve the target path relative to current working directory
  const resolvedPath = resolve(process.cwd(), targetPath);
  const projectRoot = resolve(process.cwd());

  // Check if the resolved path is within the project directory
  const relativePath = relative(projectRoot, resolvedPath);
  if (relativePath.startsWith('..') || resolve(projectRoot, relativePath) !== resolvedPath) {
    throw new PersistImageError('Target path must be within the project directory', 'INVALID_PATH');
  }

  // Check if parent directory exists, create if it doesn't
  const parentDir = dirname(resolvedPath);
  try {
    await fs.access(parentDir);
  } catch {
    try {
      await fs.mkdir(parentDir, { recursive: true });
    } catch (err) {
      throw new PersistImageError(
        `Failed to create directory: ${err instanceof Error ? err.message : 'Unknown error'}`,
        'DIRECTORY_CREATE_FAILED',
      );
    }
  }

  return resolvedPath;
}

/**
 * Determines the final file path based on target path and downloaded file info
 */
function determineFinalPath(targetPath: string, url: string, mimeType: string): string {
  const hasExtension = extname(targetPath) !== '';

  if (hasExtension) {
    // If target path already has extension, use it as-is
    return targetPath;
  }

  // If no extension, treat as directory/filename and add appropriate extension
  const urlFilename = getFilenameFromUrl(url);
  const urlExtension = extname(urlFilename);

  // Use extension from URL if it exists and is valid for the MIME type
  if (urlExtension) {
    const expectedExt = getExtensionFromMimeType(mimeType);
    // Use URL extension if it matches the MIME type, otherwise use MIME type extension
    const finalExt = urlExtension.toLowerCase() === expectedExt ? urlExtension : expectedExt;
    return `${targetPath}${finalExt}`;
  }

  // Use extension based on MIME type
  const extension = getExtensionFromMimeType(mimeType);
  return `${targetPath}${extension}`;
}

/**
 * Downloads an image from URL and saves it to the specified path
 */
export async function downloadAndSaveImage({ url, targetPath }: DownloadOptions): Promise<DownloadResult> {
  // Validate target path
  const validatedPath = await validateTargetPath(targetPath);

  // Download the image
  let response: Response;
  try {
    response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; MCP-GoogleImagesSearch/1.0)',
      },
    });
  } catch (err) {
    throw new PersistImageError(`Failed to fetch image: ${err instanceof Error ? err.message : 'Network error'}`, 'FETCH_FAILED');
  }

  if (!response.ok) {
    throw new PersistImageError(`HTTP error ${response.status}: ${response.statusText}`, 'HTTP_ERROR');
  }

  // Validate content type
  const contentType = response.headers.get('content-type') || '';
  if (!ALLOWED_IMAGE_TYPES.has(contentType)) {
    throw new PersistImageError(`Invalid content type: ${contentType}. Only image files are allowed.`, 'INVALID_CONTENT_TYPE');
  }

  // Validate content length
  const contentLength = response.headers.get('content-length');
  if (contentLength && parseInt(contentLength, 10) > MAX_FILE_SIZE) {
    throw new PersistImageError(
      `File too large: ${Math.round(parseInt(contentLength, 10) / 1024 / 1024)}MB. Maximum allowed: ${MAX_FILE_SIZE / 1024 / 1024}MB`,
      'FILE_TOO_LARGE',
    );
  }

  // Determine final file path
  const finalPath = determineFinalPath(validatedPath, url, contentType);

  // Ensure the final path is still valid
  const finalValidatedPath = await validateTargetPath(relative(process.cwd(), finalPath));

  // Download and save the file
  if (!response.body) {
    throw new PersistImageError('No response body received', 'NO_RESPONSE_BODY');
  }

  try {
    const writeStream = createWriteStream(finalValidatedPath);
    await pipeline(response.body, writeStream);
  } catch (err) {
    throw new PersistImageError(`Failed to save file: ${err instanceof Error ? err.message : 'Unknown error'}`, 'SAVE_FAILED');
  }

  // Get file stats
  let fileStats;
  try {
    fileStats = await fs.stat(finalValidatedPath);
  } catch (err) {
    throw new PersistImageError(`Failed to get file stats: ${err instanceof Error ? err.message : 'Unknown error'}`, 'STAT_FAILED');
  }

  // Validate file size after download
  if (fileStats.size > MAX_FILE_SIZE) {
    // Clean up the file
    try {
      await fs.unlink(finalValidatedPath);
    } catch {
      // Ignore cleanup errors
    }
    throw new PersistImageError(
      `Downloaded file too large: ${Math.round(fileStats.size / 1024 / 1024)}MB. Maximum allowed: ${MAX_FILE_SIZE / 1024 / 1024}MB`,
      'FILE_TOO_LARGE',
    );
  }

  return {
    savedPath: relative(process.cwd(), finalValidatedPath),
    fileSize: fileStats.size,
    mimeType: contentType,
  };
}
